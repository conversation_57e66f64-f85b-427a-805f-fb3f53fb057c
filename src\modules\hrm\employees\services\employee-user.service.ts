import { Injectable, Logger } from '@nestjs/common';
import { EmployeeRepository } from '../repositories/employee.repository';
import { CreateEmployeeWithUserDto } from '../dto/create-employee-with-user.dto';
import {
  EmployeeWithUserResponseDto,
  EmployeeResponseDto,
  UserResponseDto,
} from '../dto/employee-with-user-response.dto';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import { EmployeeStatus } from '../enum/employee-status.enum';
import { UserStatus } from '@/modules/auth/enum/user-status.enum';
import { Transactional } from 'typeorm-transactional';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
// import { User } from '@/modules/auth/entities/user.entity'; // DEPRECATED: User entity sẽ được gộp vào Employee
import { EncryptionService } from '@/shared/services/encryption.service';
import { CreateUserForEmployeeDto } from '../dto/create-user-for-employee.dto';
import { LinkEmployeeToUserDto } from '../dto/link-employee-to-user.dto';
import { generateNextEmployeeCode } from '@/shared/utils/generators/employee-code-generator.util';
// import { UserRepository } from '@/modules/auth/repositories/user.repository'; // DEPRECATED: Sẽ sử dụng EmployeeRepository
import { generateRandomPassword, generateUsernameFromEmail } from '@/shared/utils/generators/password-generator.util';
import { EmailPlaceholderService } from '@/modules/email/services/email-placeholder.service';

/**
 * Service cho việc tạo nhân viên kèm tài khoản người dùng
 */
@Injectable()
export class EmployeeUserService {
  private readonly logger = new Logger(EmployeeUserService.name);

  constructor(
    private readonly employeeRepository: EmployeeRepository,
    private readonly userRepository: UserRepository,
    @InjectRepository(User)
    private readonly userEntityRepository: Repository<User>,
    private readonly encryptionService: EncryptionService,
    private readonly emailPlaceholderService: EmailPlaceholderService,
  ) {}

  /**
   * Tạo nhân viên mới kèm tài khoản người dùng
   * @param tenantId ID tenant (required for tenant isolation)
   * @param createDto Dữ liệu tạo nhân viên và tài khoản
   * @param currentUserId ID của người dùng đang thực hiện thao tác
   * @returns Thông tin nhân viên và tài khoản đã tạo
   */
  @Transactional()
  async createEmployeeWithUser(
    tenantId: number,
    createDto: CreateEmployeeWithUserDto,
    currentUserId: number,
  ): Promise<EmployeeWithUserResponseDto> {
    try {
      // Tìm mã nhân viên lớn nhất hiện có và tạo mã mới tăng dần
      const maxEmployeeNumber = await this.employeeRepository.findMaxEmployeeCodeNumber(tenantId);
      const employeeCode = generateNextEmployeeCode(maxEmployeeNumber);

      // Kiểm tra email đã tồn tại chưa
      const existingEmail = await this.userRepository.findByEmail(
        createDto.userInfo.email,
      );
      if (existingEmail) {
        throw new AppException(
          HRM_ERROR_CODES.EMAIL_EXISTS,
          `Email ${createDto.userInfo.email} đã được sử dụng`,
        );
      }

      // Mã hóa mật khẩu
      const hashedPassword = this.encryptionService.hashPassword(
        createDto.userInfo.password,
      );

      // Tạo timestamp hiện tại
      const now = Date.now();

      // Tạo tài khoản người dùng mới
      const user = await this.userRepository.create({
        email: createDto.userInfo.email,
        password: hashedPassword,
        // fullName: createDto.userInfo.fullName, // Không còn tồn tại trong User entity
        // departmentId: createDto.departmentId, // Không còn tồn tại trong User entity
        status: UserStatus.ACTIVE,
        tenantId,
        createdAt: now,
      });

      // Tạo nhân viên mới với mã đã generate
      const employee = await this.employeeRepository.create(tenantId, {
        employeeCode, // Sử dụng mã nhân viên đã tự động tạo
        employeeName: createDto.employeeName,
        departmentId: createDto.departmentId,
        jobTitle: createDto.jobTitle,
        status: createDto.status || EmployeeStatus.ACTIVE,
        createdAt: now,
        updatedAt: now,
        createdBy: currentUserId,
        updatedBy: currentUserId,
      });

      // Tạo đối tượng phản hồi
      const employeeResponse = new EmployeeResponseDto();
      employeeResponse.id = employee.id;
      employeeResponse.employeeCode = employee.employeeCode;
      employeeResponse.employeeName = employee.employeeName;
      employeeResponse.departmentId = employee.departmentId;
      employeeResponse.jobTitle = employee.jobTitle;
      employeeResponse.status = employee.status;

      const userResponse = new UserResponseDto();
      userResponse.id = user.id;
      userResponse.email = user.email;
      // userResponse.fullName = user.fullName; // Không còn tồn tại trong User entity
      userResponse.fullName = createDto.userInfo.fullName; // Sử dụng từ DTO

      const response: EmployeeWithUserResponseDto = {
        employee: employeeResponse,
        user: userResponse,
      };

      this.logger.log(`Đã tạo nhân viên và tài khoản thành công với mã nhân viên: ${employeeCode}`);
      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi tạo nhân viên và tài khoản: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_CREATE_FAILED,
        `Không thể tạo nhân viên và tài khoản: ${error.message}`,
      );
    }
  }

  /**
   * Tạo tài khoản người dùng cho nhân viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @param createDto Dữ liệu tạo tài khoản người dùng
   * @param currentUserId ID của người dùng đang thực hiện thao tác
   * @returns Thông tin người dùng đã tạo
   */
  @Transactional()
  async createUserForEmployee(
    tenantId: number,
    createDto: CreateUserForEmployeeDto,
    currentUserId: number,
  ): Promise<UserResponseDto> {
    try {
      // Kiểm tra email đã tồn tại chưa
      const existingEmail = await this.userRepository.findByEmail(
        createDto.email,
      );
      if (existingEmail) {
        throw new AppException(
          HRM_ERROR_CODES.EMAIL_EXISTS,
          `Email ${createDto.email} đã được sử dụng`,
        );
      }

      // Kiểm tra nhân viên có tồn tại không
      const employee = await this.employeeRepository.findById(
        tenantId,
        createDto.employeeId,
      );
      if (!employee) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${createDto.employeeId}`,
        );
      }

      // Xử lý mật khẩu
      let password: string;
      if (createDto.autoGeneratePassword) {
        // Tự động tạo mật khẩu
        password = generateRandomPassword(12);
      } else {
        // Sử dụng mật khẩu từ input
        if (!createDto.password) {
          throw new AppException(
            HRM_ERROR_CODES.USER_CREATION_FAILED,
            'Mật khẩu là bắt buộc khi autoGeneratePassword = false',
          );
        }
        password = createDto.password;
      }

      // Tạo username từ email
      const username = generateUsernameFromEmail(createDto.email);

      // Mã hóa mật khẩu
      const hashedPassword = this.encryptionService.hashPassword(password);

      // Tạo timestamp hiện tại
      const now = Date.now();

      // Tạo tài khoản người dùng mới
      const user = await this.userRepository.create({
        email: createDto.email,
        password: hashedPassword,
        // fullName: employee.employeeName, // Không còn tồn tại trong User entity
        // employeeId: createDto.employeeId, // Không còn tồn tại trong User entity
        status: UserStatus.ACTIVE,
        tenantId,
        createdAt: now,
        // userType: 'EMPLOYEE', // Không còn tồn tại trong User entity
      });

      // Cập nhật thông tin nhân viên
      await this.employeeRepository.update(tenantId, createDto.employeeId, {
        updatedAt: now,
        updatedBy: currentUserId,
      });

      // Gửi email thông báo tài khoản mới
      try {
        await this.emailPlaceholderService.sendEmployeeAccountCreation({
          EMAIL: createDto.email,
          EMPLOYEE_NAME: employee.employeeName,
          EMPLOYEE_ID: createDto.employeeId.toString(),
          USERNAME: employee.employeeName,
          PASSWORD: password, // Gửi mật khẩu gốc (chưa mã hóa)
          LOGIN_URL: process.env.APP_URL || 'http://localhost:3000',
        });
        this.logger.log(`Đã gửi email thông báo tài khoản mới đến: ${createDto.email}`);
      } catch (emailError) {
        this.logger.error(
          `Lỗi khi gửi email thông báo tài khoản: ${emailError.message}`,
          emailError.stack,
        );
        // Không throw lỗi email để không ảnh hưởng đến việc tạo tài khoản
      }

      // Tạo đối tượng phản hồi
      const userResponse = new UserResponseDto();
      userResponse.id = user.id;
      userResponse.username = username; // Sử dụng username đã tạo
      userResponse.email = user.email;
      // userResponse.fullName = user.fullName; // Không còn tồn tại trong User entity
      userResponse.fullName = employee.employeeName; // Sử dụng từ employee

      this.logger.log(`Đã tạo tài khoản người dùng thành công cho nhân viên ID: ${createDto.employeeId}`);
      return userResponse;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi tạo tài khoản người dùng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.USER_CREATION_FAILED,
        `Không thể tạo tài khoản người dùng: ${error.message}`,
      );
    }
  }

  /**
   * Gắn nhân viên với tài khoản người dùng
   * @param tenantId ID tenant (required for tenant isolation)
   * @param linkDto Dữ liệu gắn nhân viên với tài khoản
   * @param currentUserId ID của người dùng đang thực hiện thao tác
   * @returns Thông tin nhân viên và tài khoản đã gắn
   */
  @Transactional()
  async linkEmployeeToUser(
    tenantId: number,
    linkDto: LinkEmployeeToUserDto,
    currentUserId: number,
  ): Promise<EmployeeWithUserResponseDto> {
    try {
      // Kiểm tra nhân viên có tồn tại không
      const employee = await this.employeeRepository.findById(
        tenantId,
        linkDto.employeeId,
      );
      if (!employee) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${linkDto.employeeId}`,
        );
      }

      // Kiểm tra người dùng có tồn tại không
      const user = await this.userRepository.findById(linkDto.userId);
      if (!user) {
        throw new AppException(
          HRM_ERROR_CODES.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${linkDto.userId}`,
        );
      }

      // Kiểm tra người dùng đã gắn với nhân viên khác chưa
      // if (user.employeeId) { // Không còn tồn tại trong User entity
      //   throw new AppException(
      //     HRM_ERROR_CODES.USER_ALREADY_HAS_EMPLOYEE,
      //     `Người dùng với ID ${linkDto.userId} đã gắn với nhân viên khác`,
      //   );
      // }

      // TODO: Cần implement logic kiểm tra liên kết employee-user khác
      // Có thể sử dụng bảng riêng để lưu trữ mối quan hệ này

      // Tạo timestamp hiện tại
      const now = Date.now();

      // Cập nhật thông tin nhân viên
      await this.employeeRepository.update(tenantId, linkDto.employeeId, {
        updatedAt: now,
        updatedBy: currentUserId,
      });

      // Cập nhật employeeId cho người dùng
      // await this.userRepository.update(linkDto.userId, {
      //   employeeId: linkDto.employeeId, // Không còn tồn tại trong User entity
      // });

      // TODO: Cần implement logic lưu trữ mối quan hệ employee-user
      // Có thể sử dụng bảng riêng để lưu trữ mối quan hệ này

      // Tạo đối tượng phản hồi
      const employeeResponse = new EmployeeResponseDto();
      employeeResponse.id = employee.id;
      employeeResponse.employeeCode = employee.employeeCode;
      employeeResponse.employeeName = employee.employeeName;
      employeeResponse.departmentId = employee.departmentId;
      employeeResponse.jobTitle = employee.jobTitle;
      employeeResponse.status = employee.status;

      const userResponse = new UserResponseDto();
      userResponse.id = user.id;
      userResponse.email = user.email;
      // userResponse.fullName = user.fullName; // Không còn tồn tại trong User entity
      userResponse.fullName = null; // Hoặc lấy từ employee nếu cần

      const response: EmployeeWithUserResponseDto = {
        employee: employeeResponse,
        user: userResponse,
      };

      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi gắn nhân viên với tài khoản: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
        `Không thể gắn nhân viên với tài khoản: ${error.message}`,
      );
    }
  }
}
