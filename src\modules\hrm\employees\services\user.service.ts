import { Injectable, Logger } from '@nestjs/common';
import { UserRepository } from '@/modules/auth/repositories/user.repository';
import { EmployeeRepository } from '../repositories/employee.repository';
import { DepartmentRepository } from '../../org-units/repositories/department.repository';
import { UserQueryDto } from '../dto/user-query.dto';
import { UserResponseDto } from '../dto/user-response.dto';
import { UpdateUserEmailDto } from '../dto/update-user-email.dto';
import { BulkDeleteUserDto } from '../dto/bulk-delete-user.dto';
import { BulkDeleteUserResponseDto } from '../dto/bulk-delete-user-response.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
// import { User } from '@/modules/auth/entities/user.entity'; // DEPRECATED: User entity sẽ được gộp vào Employee

/**
 * Service xử lý logic nghiệp vụ cho người dùng
 * @deprecated Service này sẽ được thay thế bằng EmployeeService sau khi gộp User vào Employee
 */
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly employeeRepository: EmployeeRepository,
    private readonly departmentRepository: DepartmentRepository,
  ) {}

  /**
   * Lấy danh sách người dùng với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách người dùng đã phân trang
   */
  async findAllUsers(
    tenantId: number,
    query: UserQueryDto,
  ): Promise<PaginatedResult<UserResponseDto>> {
    try {
      const paginatedResult = await this.userRepository.findAll(tenantId, query);

      // Map users to response DTOs with additional information
      const items = await Promise.all(
        paginatedResult.items.map((user) => this.mapToResponseDto(tenantId, user))
      );

      return {
        items,
        meta: paginatedResult.meta,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách người dùng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.USER_FETCH_FAILED,
        `Lấy danh sách người dùng thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết người dùng
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID người dùng
   * @returns Thông tin chi tiết người dùng
   */
  async findUserById(tenantId: number, id: number): Promise<UserResponseDto> {
    try {
      const user = await this.userRepository.findById(id);
      if (!user || user.tenantId !== tenantId) {
        throw new AppException(
          HRM_ERROR_CODES.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${id}`,
        );
      }

      return await this.mapToResponseDto(tenantId, user);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy chi tiết người dùng: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        HRM_ERROR_CODES.USER_FETCH_FAILED,
        `Lấy chi tiết người dùng thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Chuyển đổi User entity sang UserResponseDto (với unified ID system)
   * @param tenantId ID tenant
   * @param user User entity
   * @returns UserResponseDto
   */
  private async mapToResponseDto(tenantId: number, user: User): Promise<UserResponseDto> {
    // Với unified ID system: User.id = Employee.id
    // Vì vậy chúng ta có thể tìm employee trực tiếp bằng user.id

    let employeeInfo: {
      employeeId: number;
      employeeCode: string;
      employeeName: string;
      departmentId: number | null;
      departmentName?: string;
      position: string | null;
      dateOfBirth: number | null;
      gender: string | null;
    } | null = null;

    try {
      // Với unified ID system, tìm employee bằng user.id trực tiếp
      this.logger.log(`🔗 [UNIFIED ID] Tìm employee với unified ID: ${user.id} (User.id = Employee.id)`);

      const employee = await this.employeeRepository.findById(tenantId, user.id);
      if (employee) {
        this.logger.log(`✅ [UNIFIED ID] Tìm thấy employee: ${employee.employeeName} với unified ID: ${user.id}`);

        employeeInfo = {
          employeeId: employee.id, // = user.id trong unified system
          employeeCode: employee.employeeCode,
          employeeName: employee.employeeName,
          departmentId: employee.departmentId,
          position: employee.jobTitle,
          dateOfBirth: employee.dateOfBirth ? employee.dateOfBirth.getTime() : null,
          gender: employee.gender,
        };

        // Lấy thông tin department nếu có
        if (employee.departmentId && employeeInfo) {
          try {
            const department = await this.departmentRepository.findById(tenantId, employee.departmentId);
            if (department) {
              employeeInfo.departmentName = department.name;
            }
          } catch (error) {
            this.logger.warn(`Could not fetch department info for departmentId: ${employee.departmentId}`);
          }
        }
      } else {
        this.logger.warn(`⚠️ [UNIFIED ID] Không tìm thấy employee với unified ID: ${user.id}`);
      }
    } catch (error) {
      this.logger.warn(`❌ [UNIFIED ID] Lỗi khi tìm employee với unified ID ${user.id}: ${error.message}`);
    }

    return {
      id: user.id,
      email: user.email,
      // Thông tin từ User entity (đã đơn giản hóa)
      fullName: null, // User entity không còn fullName
      status: user.status,
      createdAt: user.createdAt,
      // Thông tin từ Employee entity (nếu có liên kết)
      employeeId: employeeInfo?.employeeId || null,
      employeeCode: employeeInfo?.employeeCode || null,
      employeeName: employeeInfo?.employeeName || null,
      departmentId: employeeInfo?.departmentId || null,
      departmentName: employeeInfo?.departmentName || null,
      position: employeeInfo?.position || null,
      dateOfBirth: employeeInfo?.dateOfBirth || null,
      gender: employeeInfo?.gender || null,
      // Các field không có trong cả User và Employee entity
      phoneNumber: null, // Có thể thêm vào Employee entity sau
      address: null, // Có thể thêm vào Employee entity sau
      userType: null, // Có thể derive từ role/permission
    };
  }

  /**
   * Cập nhật email người dùng
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng
   * @param updateEmailDto Dữ liệu email mới
   * @returns Thông tin người dùng đã cập nhật
   */
  async updateUserEmail(
    tenantId: number,
    userId: number,
    updateEmailDto: UpdateUserEmailDto,
  ): Promise<UserResponseDto> {
    try {
      // Kiểm tra người dùng tồn tại và thuộc đúng tenant
      const user = await this.userRepository.findById(userId);
      if (!user || user.tenantId !== tenantId) {
        throw new AppException(
          HRM_ERROR_CODES.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${userId}`,
        );
      }

      // Kiểm tra email mới chưa được sử dụng
      const existingUser = await this.userRepository.findByEmail(updateEmailDto.email);
      if (existingUser && existingUser.id !== userId) {
        throw new AppException(
          HRM_ERROR_CODES.EMAIL_EXISTS,
          `Email ${updateEmailDto.email} đã được sử dụng bởi người dùng khác`,
        );
      }

      // Lưu email cũ để gửi thông báo
      const oldEmail = user.email;

      // Cập nhật email trong database
      const updatedUser = await this.userRepository.update(userId, {
        email: updateEmailDto.email,
      });

      // Ghi log thông báo thay đổi email thành công
      this.logger.log(
        `Email người dùng ${userId} đã được thay đổi từ ${oldEmail} thành ${updateEmailDto.email}`,
      );

      // TODO: Gửi email thông báo thay đổi email (cần tạo template email riêng)
      // Hiện tại chỉ ghi log để tránh lỗi khi chưa có template email phù hợp

      // Trả về thông tin người dùng đã cập nhật
      return await this.mapToResponseDto(tenantId, updatedUser);
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật email người dùng: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        HRM_ERROR_CODES.USER_CREATION_FAILED,
        `Cập nhật email người dùng thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa người dùng (soft delete)
   * @param tenantId ID tenant (required for tenant isolation)
   * @param userId ID người dùng cần xóa
   * @returns True nếu xóa thành công
   */
  async deleteUser(tenantId: number, userId: number): Promise<boolean> {
    try {
      // Kiểm tra người dùng tồn tại và thuộc đúng tenant
      const user = await this.userRepository.findById(userId);
      if (!user || user.tenantId !== tenantId) {
        throw new AppException(
          HRM_ERROR_CODES.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${userId}`,
        );
      }

      // Kiểm tra người dùng chưa bị xóa
      if (user.status === 'deleted') {
        throw new AppException(
          HRM_ERROR_CODES.USER_NOT_FOUND,
          `Người dùng với ID ${userId} đã bị xóa`,
        );
      }

      // Xóa người dùng (soft delete)
      const deleted = await this.userRepository.delete(tenantId, userId);
      if (!deleted) {
        throw new AppException(
          HRM_ERROR_CODES.USER_CREATION_FAILED,
          `Xóa người dùng thất bại`,
        );
      }

      this.logger.log(`Đã xóa người dùng ID: ${userId} trong tenant ${tenantId}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa người dùng: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        HRM_ERROR_CODES.USER_CREATION_FAILED,
        `Xóa người dùng thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều người dùng (bulk delete)
   * @param tenantId ID tenant (required for tenant isolation)
   * @param dto DTO chứa danh sách ID người dùng cần xóa
   * @returns Kết quả xóa nhiều người dùng
   */
  async bulkDeleteUsers(
    tenantId: number,
    dto: BulkDeleteUserDto,
  ): Promise<BulkDeleteUserResponseDto> {
    try {
      const { ids } = dto;

      // Tìm tất cả người dùng theo danh sách ID và thuộc tenant
      const users = await this.userRepository.findByIds(ids);
      const validUsers = users.filter(
        (user) => user.tenantId === tenantId && user.status !== 'deleted',
      );
      const validIds = validUsers.map((user) => user.id);
      const invalidIds = ids.filter((id) => !validIds.includes(id));

      const result: BulkDeleteUserResponseDto = {
        totalRequested: ids.length,
        successCount: 0,
        failureCount: 0,
        deletedIds: [],
        failures: [],
      };

      // Thêm lỗi cho các ID không hợp lệ
      invalidIds.forEach((id) => {
        result.failures.push({
          id,
          reason: 'Người dùng không tồn tại hoặc đã bị xóa',
        });
        result.failureCount++;
      });

      // Xóa các người dùng hợp lệ
      if (validIds.length > 0) {
        const deletedCount = await this.userRepository.bulkDelete(tenantId, validIds);
        result.successCount = deletedCount;
        result.deletedIds = validIds.slice(0, deletedCount);

        // Nếu số lượng xóa thành công ít hơn số lượng yêu cầu
        if (deletedCount < validIds.length) {
          const failedIds = validIds.slice(deletedCount);
          failedIds.forEach((id) => {
            result.failures.push({
              id,
              reason: 'Lỗi khi xóa người dùng',
            });
            result.failureCount++;
          });
        }
      }

      this.logger.log(
        `Bulk delete users: ${result.successCount}/${result.totalRequested} thành công`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa nhiều người dùng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.USER_CREATION_FAILED,
        `Xóa nhiều người dùng thất bại: ${error.message}`,
      );
    }
  }
}
