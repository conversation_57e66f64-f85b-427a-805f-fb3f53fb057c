import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
  Patch,
  Body,
  Delete,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { UserService } from '../services/user.service';
import { UserQueryDto } from '../dto/user-query.dto';
import { UserResponseDto } from '../dto/user-response.dto';
import { UpdateUserEmailDto } from '../dto/update-user-email.dto';
import { BulkDeleteUserDto } from '../dto/bulk-delete-user.dto';
import { BulkDeleteUserResponseDto } from '../dto/bulk-delete-user-response.dto';

/**
 * Controller cho việc quản lý người dùng
 * @deprecated Controller này sẽ được thay thế bằng EmployeeController sau khi gộp User vào Employee
 */
@ApiTags(SWAGGER_API_TAG.EMPLOYEE)
@ApiExtraModels(
  ApiResponseDto,
  UserResponseDto,
  UpdateUserEmailDto,
  BulkDeleteUserDto,
  BulkDeleteUserResponseDto,
)
@Controller('api/hrm/employees/users-management')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserController {
  constructor(private readonly userService: UserService) {}

  /**
   * Lấy danh sách người dùng
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách người dùng với phân trang và lọc' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách người dùng',
    schema: ApiResponseDto.getPaginatedSchema(UserResponseDto),
  })
  async findAllUsers(
    @CurrentUser() user: JwtPayload,
    @Query() query: UserQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserResponseDto>>> {
    const paginatedUsers = await this.userService.findAllUsers(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.paginated(
      paginatedUsers,
      'Lấy danh sách người dùng thành công',
    );
  }

  /**
   * Lấy chi tiết người dùng
   */
  @Get('/detail/:id')
  @ApiOperation({ summary: 'Lấy chi tiết người dùng' })
  @ApiParam({ name: 'id', description: 'ID người dùng', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết người dùng',
    schema: ApiResponseDto.getSchema(UserResponseDto),
  })
  async findUserById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    const userDetail = await this.userService.findUserById(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(
      userDetail,
      'Lấy chi tiết người dùng thành công',
    );
  }

  /**
   * Cập nhật email người dùng
   */
  @Patch('/update-email/:id')
  @ApiOperation({ summary: 'Cập nhật email người dùng' })
  @ApiParam({ name: 'id', description: 'ID người dùng', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật email thành công',
    schema: ApiResponseDto.getSchema(UserResponseDto),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc email đã tồn tại' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy người dùng' })
  async updateUserEmail(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateEmailDto: UpdateUserEmailDto,
  ): Promise<ApiResponseDto<UserResponseDto>> {
    const updatedUser = await this.userService.updateUserEmail(
      Number(user.tenantId),
      id,
      updateEmailDto,
    );
    return ApiResponseDto.success(
      updatedUser,
      'Cập nhật email người dùng thành công',
    );
  }

  /**
   * Xóa người dùng
   */
  @Delete('/:id')
  @ApiOperation({ summary: 'Xóa người dùng (soft delete)' })
  @ApiParam({ name: 'id', description: 'ID người dùng', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Xóa người dùng thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  @ApiResponse({ status: 404, description: 'Không tìm thấy người dùng' })
  async deleteUser(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.userService.deleteUser(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(
      result,
      'Xóa người dùng thành công',
    );
  }

  /**
   * Xóa nhiều người dùng
   */
  @Delete('/bulk-delete')
  @ApiOperation({ summary: 'Xóa nhiều người dùng (bulk soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa nhiều người dùng',
    schema: ApiResponseDto.getSchema(BulkDeleteUserResponseDto),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  async bulkDeleteUsers(
    @CurrentUser() user: JwtPayload,
    @Body() bulkDeleteDto: BulkDeleteUserDto,
  ): Promise<ApiResponseDto<BulkDeleteUserResponseDto>> {
    const result = await this.userService.bulkDeleteUsers(
      Number(user.tenantId),
      bulkDeleteDto,
    );
    return ApiResponseDto.success(
      result,
      'Xóa nhiều người dùng hoàn tất',
    );
  }
}
